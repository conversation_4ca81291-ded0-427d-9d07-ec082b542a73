# 📦 Distribución de Componentes

Esta guía explica cómo **publicar, instalar y usar paquetes privados** en GitHub Packages, y cómo optimizar el desarrollo local.

---

## 1️⃣ Preparar el paquete

Cada paquete debe llevar un **scope** con tu usuario u organización en GitHub:

```json
{
	"name": "@luinux81/astro-button-base",
	"version": "1.0.0",
	"main": "src/ButtonBase.astro",
	"files": ["src/"]
}
```

---

## 2️⃣ Autenticación con GitHub Packages

-  **Local (máquina personal):**
   `npm login --registry=https://npm.pkg.github.com --scope=@luinux81`

-  **Proyecto / CI-CD (`.npmrc`):**

   ```
   @luinux81:registry=https://npm.pkg.github.com
   //npm.pkg.github.com/:_authToken=TOKEN
   ```

---

## 3️⃣ Publicar un paquete

```bash
cd packages/atoms/button-base/astro
npm publish --registry=https://npm.pkg.github.com
```

---

## 4️⃣ Instalar en otro proyecto

-  **Un solo componente:**
   `npm install @luinux81/astro-button-base`

-  **Bundle completo:**
   `npm install @luinux81/astro-atoms`

---

## 5️⃣ Uso en código

```astro
---
import ButtonBase from "@luinux81/astro-button-base";
---

<ButtonBase label="Enviar" />
```

---

## 6️⃣ CI/CD con GitHub Actions

En tu workflow, añade:

```yaml
- name: Configure .npmrc
  run: |
     echo "@luinux81:registry=https://npm.pkg.github.com" > .npmrc
     echo "//npm.pkg.github.com/:_authToken=${NODE_AUTH_TOKEN}" >> .npmrc
  env:
     NODE_AUTH_TOKEN: ${{ secrets.PERSONAL_ACCESS_TOKEN_GITHUB }}
```

Esto permite instalar y publicar paquetes privados en Actions.

---

## 7️⃣ Desarrollo local más rápido

### Opción 1: `npm link`

En el paquete:
`npm link`

En el proyecto consumidor:
`npm link @luinux81/astro-button-base`

👉 Los cambios se reflejan sin publicar.

### Opción 2: Workspaces (recomendado a medio plazo)

Configura `pnpm-workspace.yaml` para compartir paquetes sin publicarlos.

---

## ✅ Resumen

1. Scope del paquete = tu usuario/org.
2. `.npmrc` → controla autenticación local o en CI/CD.
3. `npm publish` → sube tu paquete privado.
4. `npm install` → lo usas en otros proyectos.
5. `npm link` → desarrollo rápido sin publicar.
6. Workspaces → opción más escalable para el futuro.
