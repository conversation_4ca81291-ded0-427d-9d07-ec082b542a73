---
import "../styles/global.css";

// Importar componentes
import ButtonIcon from "@luinux81/astro-button-icon";
import ButtonAnimated from "@luinux81/astro-button-animated";
---

<!doctype html>
<html lang="es">
	<head>
		<title>Test - Componentes ButtonIcon y ButtonAnimated</title>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		
		<!-- Material Symbols -->
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
		/>
		<link
			href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
			rel="stylesheet"
		/>
	</head>
	<body class="bg-gray-50 font-sans p-8">
		<div class="max-w-4xl mx-auto">
			<h1 class="text-3xl font-bold text-gray-900 mb-8">
				🧪 Test de Componentes
			</h1>

			<!-- Test ButtonIcon -->
			<section class="mb-12">
				<h2 class="text-2xl font-semibold text-gray-800 mb-6">ButtonIcon</h2>
				
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
					<div class="text-center">
						<ButtonIcon label="Inicio">
							<span class="material-symbols-outlined">home</span>
						</ButtonIcon>
						<p class="text-sm text-gray-600 mt-2">Default</p>
					</div>
					
					<div class="text-center">
						<ButtonIcon label="Buscar" variant="ghost">
							<span class="material-symbols-outlined">search</span>
						</ButtonIcon>
						<p class="text-sm text-gray-600 mt-2">Ghost</p>
					</div>
					
					<div class="text-center">
						<ButtonIcon label="Configuración" variant="link">
							<span class="material-symbols-outlined">settings</span>
						</ButtonIcon>
						<p class="text-sm text-gray-600 mt-2">Link</p>
					</div>
					
					<div class="text-center">
						<ButtonIcon label="Perfil" size="lg">
							<span class="material-symbols-outlined">person</span>
						</ButtonIcon>
						<p class="text-sm text-gray-600 mt-2">Large</p>
					</div>
				</div>
			</section>

			<!-- Test ButtonAnimated -->
			<section class="mb-12">
				<h2 class="text-2xl font-semibold text-gray-800 mb-6">ButtonAnimated</h2>
				
				<div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
					<div class="text-center">
						<ButtonAnimated
							id="test-hamburger"
							animation="hamburger"
							size="md"
							color="gray-700"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Hamburger</p>
					</div>
					
					<div class="text-center">
						<ButtonAnimated
							id="test-plus"
							animation="plus"
							size="md"
							color="blue-600"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Plus</p>
					</div>
					
					<div class="text-center">
						<ButtonAnimated
							id="test-arrow"
							animation="arrow"
							size="md"
							color="green-600"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Arrow</p>
					</div>
					
					<div class="text-center">
						<ButtonAnimated
							id="test-dots"
							animation="dots"
							size="md"
							color="purple-600"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Dots</p>
					</div>
				</div>
				
				<!-- Test tamaños -->
				<div class="flex items-center justify-center space-x-8 mb-8">
					<div class="text-center">
						<ButtonAnimated
							id="test-sm"
							animation="hamburger"
							size="sm"
							color="gray-700"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Small</p>
					</div>
					
					<div class="text-center">
						<ButtonAnimated
							id="test-md"
							animation="hamburger"
							size="md"
							color="gray-700"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Medium</p>
					</div>
					
					<div class="text-center">
						<ButtonAnimated
							id="test-lg"
							animation="hamburger"
							size="lg"
							color="gray-700"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Large</p>
					</div>
					
					<div class="text-center">
						<ButtonAnimated
							id="test-xl"
							animation="hamburger"
							size="xl"
							color="gray-700"
							toggleOnClick={true}
						/>
						<p class="text-sm text-gray-600 mt-2">Extra Large</p>
					</div>
				</div>
				
				<!-- Test custom slot -->
				<div class="text-center">
					<ButtonAnimated
						id="test-custom"
						animation="custom"
						size="lg"
						color="red-600"
						toggleOnClick={true}
					>
						<span class="material-symbols-outlined">favorite</span>
					</ButtonAnimated>
					<p class="text-sm text-gray-600 mt-2">Custom con slot</p>
				</div>
			</section>

			<!-- Instrucciones -->
			<section class="bg-white rounded-lg p-6 shadow-sm">
				<h3 class="text-lg font-semibold text-gray-800 mb-4">
					🎯 Instrucciones de prueba
				</h3>
				<ul class="list-disc list-inside space-y-2 text-gray-600">
					<li>Los botones con iconos deben mostrar los iconos de Material Symbols correctamente</li>
					<li>Los botones animados deben cambiar de estado al hacer clic</li>
					<li>Las animaciones deben ser suaves y visibles</li>
					<li>Los diferentes tamaños deben ser claramente distinguibles</li>
					<li>El slot personalizado debe mostrar el icono de corazón</li>
				</ul>
			</section>
		</div>
	</body>
</html>
